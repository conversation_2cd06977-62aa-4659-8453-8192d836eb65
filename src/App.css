#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

.fade {
  opacity: 0.3;
}

.item-enter {
  animation: itemEnter 0.4s cubic-bezier(0.22, 1, 0.36, 1);
}
@keyframes itemEnter {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  80% {
    opacity: 1;
    transform: scale(1.05) translateY(-4px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.item-update {
  animation: itemUpdate 0.4s cubic-bezier(0.22, 1, 0.36, 1);
}
@keyframes itemUpdate {
  0% {
    background: #ffe066;
    transform: scale(1.05);
  }
  100% {
    background: inherit;
    transform: scale(1);
  }
}

.item-exit {
  animation: itemExit 0.4s cubic-bezier(0.22, 1, 0.36, 1);
}
@keyframes itemExit {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.7) translateX(40px);
  }
}
